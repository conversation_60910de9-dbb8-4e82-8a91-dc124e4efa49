namespace Common.Exceptions
{
    /// <summary>
    /// 错误码常量类
    /// 统一管理所有业务错误码和对应的错误消息
    /// </summary>
    public static class ErrorCodes
    {
        #region HTTP标准错误码

        /// <summary>
        /// 请求参数错误
        /// </summary>
        public const int BAD_REQUEST = 400;

        /// <summary>
        /// 未授权访问
        /// </summary>
        public const int UNAUTHORIZED = 401;

        /// <summary>
        /// 权限不足
        /// </summary>
        public const int FORBIDDEN = 403;

        /// <summary>
        /// 资源未找到
        /// </summary>
        public const int NOT_FOUND = 404;

        /// <summary>
        /// 资源冲突
        /// </summary>
        public const int CONFLICT = 409;

        /// <summary>
        /// 数据验证失败
        /// </summary>
        public const int UNPROCESSABLE_ENTITY = 422;

        /// <summary>
        /// 业务逻辑错误
        /// </summary>
        public const int INTERNAL_ERROR = 500;

        #endregion

        #region 业务特定错误码

        // 用户相关错误码 (1000-1999)
        /// <summary>
        /// 用户不存在
        /// </summary>
        public const int USER_NOT_FOUND = 1001;

        /// <summary>
        /// 用户已存在
        /// </summary>
        public const int USER_ALREADY_EXISTS = 1002;

        /// <summary>
        /// 用户状态异常
        /// </summary>
        public const int USER_STATUS_INVALID = 1003;

        /// <summary>
        /// 用户权限不足
        /// </summary>
        public const int USER_PERMISSION_DENIED = 1004;

        // 视频相关错误码 (2000-2999)
        /// <summary>
        /// 视频不存在
        /// </summary>
        public const int VIDEO_NOT_FOUND = 2001;

        /// <summary>
        /// 视频格式不支持
        /// </summary>
        public const int VIDEO_FORMAT_UNSUPPORTED = 2002;

        /// <summary>
        /// 视频文件过大
        /// </summary>
        public const int VIDEO_FILE_TOO_LARGE = 2003;

        /// <summary>
        /// 视频处理失败
        /// </summary>
        public const int VIDEO_PROCESSING_FAILED = 2004;

        // 批次相关错误码 (3000-3999)
        /// <summary>
        /// 批次不存在
        /// </summary>
        public const int BATCH_NOT_FOUND = 3001;

        /// <summary>
        /// 批次已结束
        /// </summary>
        public const int BATCH_EXPIRED = 3002;

        /// <summary>
        /// 批次未开始
        /// </summary>
        public const int BATCH_NOT_STARTED = 3003;

        /// <summary>
        /// 批次状态异常
        /// </summary>
        public const int BATCH_STATUS_INVALID = 3004;

        // 答题相关错误码 (4000-4999)
        /// <summary>
        /// 答题时间已过
        /// </summary>
        public const int ANSWER_TIME_EXPIRED = 4001;

        /// <summary>
        /// 重复答题
        /// </summary>
        public const int ANSWER_DUPLICATE = 4002;

        /// <summary>
        /// 答案格式错误
        /// </summary>
        public const int ANSWER_FORMAT_INVALID = 4003;

        // 文件相关错误码 (5000-5999)
        /// <summary>
        /// 文件不存在
        /// </summary>
        public const int FILE_NOT_FOUND = 5001;

        /// <summary>
        /// 文件格式不支持
        /// </summary>
        public const int FILE_FORMAT_UNSUPPORTED = 5002;

        /// <summary>
        /// 文件过大
        /// </summary>
        public const int FILE_TOO_LARGE = 5003;

        /// <summary>
        /// 文件上传失败
        /// </summary>
        public const int FILE_UPLOAD_FAILED = 5004;

        #endregion

        #region 错误消息映射

        /// <summary>
        /// 错误码与消息的映射字典
        /// </summary>
        public static readonly Dictionary<int, string> ErrorMessages = new()
        {
            // HTTP标准错误
            { BAD_REQUEST, "请求参数错误" },
            { UNAUTHORIZED, "未授权访问" },
            { FORBIDDEN, "权限不足" },
            { NOT_FOUND, "资源未找到" },
            { CONFLICT, "资源冲突" },
            { UNPROCESSABLE_ENTITY, "数据验证失败" },
            { INTERNAL_ERROR, "业务处理失败" },

            // 用户相关错误
            { USER_NOT_FOUND, "用户不存在" },
            { USER_ALREADY_EXISTS, "用户已存在" },
            { USER_STATUS_INVALID, "用户状态异常" },
            { USER_PERMISSION_DENIED, "用户权限不足" },

            // 视频相关错误
            { VIDEO_NOT_FOUND, "视频不存在" },
            { VIDEO_FORMAT_UNSUPPORTED, "视频格式不支持" },
            { VIDEO_FILE_TOO_LARGE, "视频文件过大" },
            { VIDEO_PROCESSING_FAILED, "视频处理失败" },

            // 批次相关错误
            { BATCH_NOT_FOUND, "批次不存在" },
            { BATCH_EXPIRED, "批次已结束" },
            { BATCH_NOT_STARTED, "批次未开始" },
            { BATCH_STATUS_INVALID, "批次状态异常" },

            // 答题相关错误
            { ANSWER_TIME_EXPIRED, "答题时间已过" },
            { ANSWER_DUPLICATE, "重复答题" },
            { ANSWER_FORMAT_INVALID, "答案格式错误" },

            // 文件相关错误
            { FILE_NOT_FOUND, "文件不存在" },
            { FILE_FORMAT_UNSUPPORTED, "文件格式不支持" },
            { FILE_TOO_LARGE, "文件过大" },
            { FILE_UPLOAD_FAILED, "文件上传失败" }
        };

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取错误码对应的消息
        /// </summary>
        /// <param name="errorCode">错误码</param>
        /// <returns>错误消息</returns>
        public static string GetMessage(int errorCode)
        {
            return ErrorMessages.TryGetValue(errorCode, out var message) ? message : "未知错误";
        }

        /// <summary>
        /// 判断是否为HTTP标准错误码
        /// </summary>
        /// <param name="errorCode">错误码</param>
        /// <returns>是否为HTTP标准错误码</returns>
        public static bool IsHttpStatusCode(int errorCode)
        {
            return errorCode >= 400 && errorCode < 600;
        }

        /// <summary>
        /// 判断是否为业务特定错误码
        /// </summary>
        /// <param name="errorCode">错误码</param>
        /// <returns>是否为业务特定错误码</returns>
        public static bool IsBusinessCode(int errorCode)
        {
            return errorCode >= 1000 && errorCode < 10000;
        }

        #endregion
    }
}
