using Common.Exceptions;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.BasisController;

namespace ServiceVideoSharing.Controllers.TestControllers
{
    /// <summary>
    /// 错误处理测试控制器
    /// 用于测试全局错误拦截机制的各种异常类型处理
    /// </summary>
    [ApiController]
    [Route("api/test/[controller]")]
    public class ErrorTestController : BaseController
    {
        /// <summary>
        /// 测试业务异常 - 应该返回HTTP 200，响应体code 500
        /// </summary>
        [HttpGet("business-exception")]
        public IActionResult TestBusinessException()
        {
            throw new BusinessException("这是一个业务逻辑错误测试");
        }

        /// <summary>
        /// 测试业务异常（自定义错误码） - 应该返回HTTP 200，响应体code 400
        /// </summary>
        [HttpGet("business-exception-400")]
        public IActionResult TestBusinessException400()
        {
            throw BusinessException.BadRequest("请求参数错误测试");
        }

        /// <summary>
        /// 测试业务异常（用户不存在） - 应该返回HTTP 200，响应体code 1001
        /// </summary>
        [HttpGet("user-not-found")]
        public IActionResult TestUserNotFound()
        {
            throw BusinessException.UserNotFound("测试用户不存在");
        }

        /// <summary>
        /// 测试授权异常 - 应该返回HTTP 401，响应体code 401
        /// </summary>
        [HttpGet("authorization-exception")]
        public IActionResult TestAuthorizationException()
        {
            throw new AuthorizationException("权限验证失败测试");
        }

        /// <summary>
        /// 测试参数异常 - 应该返回HTTP 400，响应体code 400
        /// </summary>
        [HttpGet("argument-exception")]
        public IActionResult TestArgumentException()
        {
            throw new ArgumentException("参数错误测试");
        }

        /// <summary>
        /// 测试系统异常 - 应该返回HTTP 500，响应体code 500
        /// </summary>
        [HttpGet("system-exception")]
        public IActionResult TestSystemException()
        {
            throw new InvalidOperationException("这是一个系统级别错误测试");
        }

        /// <summary>
        /// 测试空指针异常 - 应该返回HTTP 500，响应体code 500
        /// </summary>
        [HttpGet("null-reference-exception")]
        public IActionResult TestNullReferenceException()
        {
            string? nullString = null;
            return Ok(nullString.Length); // 这会抛出NullReferenceException
        }

        /// <summary>
        /// 测试正常响应 - 应该返回HTTP 200，响应体code 200
        /// </summary>
        [HttpGet("success")]
        public IActionResult TestSuccess()
        {
            return Ok(Success("测试成功响应"));
        }

        /// <summary>
        /// 测试使用错误码创建异常
        /// </summary>
        [HttpGet("error-code/{code}")]
        public IActionResult TestErrorCode(int code)
        {
            throw BusinessException.WithCode(code, $"测试错误码 {code}");
        }

        /// <summary>
        /// 测试视频不存在异常
        /// </summary>
        [HttpGet("video-not-found")]
        public IActionResult TestVideoNotFound()
        {
            throw BusinessException.VideoNotFound("测试视频ID不存在");
        }

        /// <summary>
        /// 测试批次不存在异常
        /// </summary>
        [HttpGet("batch-not-found")]
        public IActionResult TestBatchNotFound()
        {
            throw BusinessException.BatchNotFound("测试批次ID不存在");
        }

        /// <summary>
        /// 获取所有错误码信息
        /// </summary>
        [HttpGet("error-codes")]
        public IActionResult GetErrorCodes()
        {
            var errorCodes = new
            {
                HttpStatusCodes = new
                {
                    BadRequest = ErrorCodes.BAD_REQUEST,
                    Unauthorized = ErrorCodes.UNAUTHORIZED,
                    Forbidden = ErrorCodes.FORBIDDEN,
                    NotFound = ErrorCodes.NOT_FOUND,
                    Conflict = ErrorCodes.CONFLICT,
                    UnprocessableEntity = ErrorCodes.UNPROCESSABLE_ENTITY,
                    InternalError = ErrorCodes.INTERNAL_ERROR
                },
                BusinessCodes = new
                {
                    UserNotFound = ErrorCodes.USER_NOT_FOUND,
                    UserAlreadyExists = ErrorCodes.USER_ALREADY_EXISTS,
                    VideoNotFound = ErrorCodes.VIDEO_NOT_FOUND,
                    VideoFormatUnsupported = ErrorCodes.VIDEO_FORMAT_UNSUPPORTED,
                    BatchNotFound = ErrorCodes.BATCH_NOT_FOUND,
                    BatchExpired = ErrorCodes.BATCH_EXPIRED
                },
                Messages = ErrorCodes.ErrorMessages
            };

            return Ok(Success(errorCodes, "错误码信息获取成功"));
        }
    }
}
