using Common.Exceptions;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.BasisController;

namespace ServiceVideoSharing.Controllers.TestControllers
{
    /// <summary>
    /// 错误处理测试控制器
    /// 用于测试全局错误拦截机制：200正常，500错误，401没有权限
    /// </summary>
    [ApiController]
    [Route("api/test/[controller]")]
    public class ErrorTestController : BaseController
    {
        /// <summary>
        /// 测试业务异常 - 应该返回HTTP 200，响应体code 500
        /// </summary>
        [HttpGet("business-exception")]
        public IActionResult TestBusinessException()
        {
            throw new BusinessException("这是一个业务逻辑错误测试");
        }

        /// <summary>
        /// 测试授权异常 - 应该返回HTTP 401，响应体code 401
        /// </summary>
        [HttpGet("authorization-exception")]
        public IActionResult TestAuthorizationException()
        {
            throw new AuthorizationException("权限验证失败测试");
        }

        /// <summary>
        /// 测试系统异常 - 应该返回HTTP 500，响应体code 500
        /// </summary>
        [HttpGet("system-exception")]
        public IActionResult TestSystemException()
        {
            throw new InvalidOperationException("这是一个系统级别错误测试");
        }

        /// <summary>
        /// 测试正常响应 - 应该返回HTTP 200，响应体code 200
        /// </summary>
        [HttpGet("success")]
        public IActionResult TestSuccess()
        {
            return Ok(Success("测试成功响应"));
        }

        /// <summary>
        /// 获取错误码信息
        /// </summary>
        [HttpGet("error-codes")]
        public IActionResult GetErrorCodes()
        {
            var errorCodes = new
            {
                Success = ErrorCodes.SUCCESS,
                Unauthorized = ErrorCodes.UNAUTHORIZED,
                Error = ErrorCodes.ERROR,
                Messages = ErrorCodes.ErrorMessages
            };

            return Ok(Success(errorCodes, "错误码信息获取成功"));
        }
    }
}
