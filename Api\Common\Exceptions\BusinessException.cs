namespace Common.Exceptions
{
    /// <summary>
    /// 业务异常类，用于表示业务逻辑错误
    /// </summary>
    public class BusinessException : Exception
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; }

        /// <summary>
        /// 错误级别
        /// </summary>
        public ErrorLevel Level { get; }

        /// <summary>
        /// 创建业务异常实例
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="errorCode">错误代码，默认为400</param>
        /// <param name="level">错误级别，默认为警告</param>
        public BusinessException(string message, int errorCode = 500, ErrorLevel level = ErrorLevel.Warning)
            : base(message)
        {
            ErrorCode = errorCode;
            Level = level;
        }

        /// <summary>
        /// 创建业务异常实例
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        /// <param name="errorCode">错误代码，默认为400</param>
        /// <param name="level">错误级别，默认为警告</param>
        public BusinessException(string message, Exception innerException, int errorCode = 500, ErrorLevel level = ErrorLevel.Warning)
            : base(message, innerException)
        {
            ErrorCode = errorCode;
            Level = level;
        }

        #region 静态工厂方法

        /// <summary>
        /// 创建400错误（请求参数错误）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException BadRequest(string message = "请求参数错误")
            => new(message, 400, ErrorLevel.Warning);

        /// <summary>
        /// 创建401错误（未授权）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException Unauthorized(string message = "未授权访问")
            => new(message, 401, ErrorLevel.Warning);

        /// <summary>
        /// 创建403错误（禁止访问）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException Forbidden(string message = "权限不足")
            => new(message, 403, ErrorLevel.Warning);

        /// <summary>
        /// 创建404错误（资源未找到）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException NotFound(string message = "资源未找到")
            => new(message, 404, ErrorLevel.Warning);

        /// <summary>
        /// 创建409错误（资源冲突）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException Conflict(string message = "资源冲突")
            => new(message, 409, ErrorLevel.Warning);

        /// <summary>
        /// 创建422错误（实体无法处理）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException UnprocessableEntity(string message = "数据验证失败")
            => new(message, 422, ErrorLevel.Warning);

        /// <summary>
        /// 创建500错误（业务逻辑错误）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException InternalError(string message = "业务处理失败")
            => new(message, 500, ErrorLevel.Error);

        /// <summary>
        /// 使用错误码创建业务异常
        /// </summary>
        /// <param name="errorCode">错误码</param>
        /// <param name="customMessage">自定义错误消息（可选）</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException WithCode(int errorCode, string? customMessage = null)
        {
            var message = customMessage ?? ErrorCodes.GetMessage(errorCode);
            var level = ErrorCodes.IsHttpStatusCode(errorCode) ? ErrorLevel.Warning : ErrorLevel.Error;
            return new BusinessException(message, errorCode, level);
        }

        /// <summary>
        /// 创建用户相关错误
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException UserNotFound(string message = "用户不存在")
            => WithCode(ErrorCodes.USER_NOT_FOUND, message);

        /// <summary>
        /// 创建视频相关错误
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException VideoNotFound(string message = "视频不存在")
            => WithCode(ErrorCodes.VIDEO_NOT_FOUND, message);

        /// <summary>
        /// 创建批次相关错误
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>BusinessException实例</returns>
        public static BusinessException BatchNotFound(string message = "批次不存在")
            => WithCode(ErrorCodes.BATCH_NOT_FOUND, message);

        #endregion
    }

    /// <summary>
    /// 错误级别枚举
    /// </summary>
    public enum ErrorLevel
    {
        /// <summary>
        /// 信息级别 - 不严重的问题
        /// </summary>
        Info,

        /// <summary>
        /// 警告级别 - 需要注意但不会导致程序崩溃的问题
        /// </summary>
        Warning,

        /// <summary>
        /// 错误级别 - 严重问题，可能影响系统部分功能
        /// </summary>
        Error,

        /// <summary>
        /// 严重错误 - 可能导致系统崩溃的致命问题
        /// </summary>
        Critical
    }
}